---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: 'bug'
assignees: ''

---

## 🐛 Bug Description
A clear and concise description of what the bug is.

## 🔄 Steps to Reproduce
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## ✅ Expected Behavior
A clear and concise description of what you expected to happen.

## ❌ Actual Behavior
A clear and concise description of what actually happened.

## 📸 Screenshots
If applicable, add screenshots to help explain your problem.

## 🖥️ Environment
- **OS**: [e.g. macOS, Windows, Linux]
- **Python Version**: [e.g. 3.11]
- **Ollama Version**: [e.g. 0.1.7]
- **Browser**: [e.g. Chrome, Firefox]
- **Application Version**: [e.g. 2.0.0]

## 📋 System Information
Please run `python test_setup.py` and paste the output:

```
[Paste test_setup.py output here]
```

## 📄 Logs
If applicable, add relevant log output:

```
[Paste relevant logs here]
```

## 🔧 Configuration
- **Chunk Size**: [e.g. 1000]
- **Embedding Provider**: [e.g. Ollama]
- **Models Used**: [e.g. llama3.1:latest, nomic-embed-text]

## 📝 Additional Context
Add any other context about the problem here.

## ✅ Checklist
- [ ] I have searched existing issues to ensure this is not a duplicate
- [ ] I have run the test suite (`python test_setup.py`)
- [ ] I have checked the troubleshooting section in README.md
- [ ] I have provided all requested information above
