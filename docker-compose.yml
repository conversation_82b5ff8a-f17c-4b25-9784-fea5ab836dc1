version: '3.8'

services:
  smart-doc-assistant:
    build: .
    ports:
      - "8501:8501"
    volumes:
      - ./data:/app/data
      - ./cache:/app/cache
      - ./chroma_db:/app/chroma_db
    environment:
      - OLLAMA_BASE_URL=http://ollama:11434
    depends_on:
      - ollama
    restart: unless-stopped
    networks:
      - rag-network

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    restart: unless-stopped
    networks:
      - rag-network
    # Uncomment if you have GPU support
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

volumes:
  ollama_data:

networks:
  rag-network:
    driver: bridge
