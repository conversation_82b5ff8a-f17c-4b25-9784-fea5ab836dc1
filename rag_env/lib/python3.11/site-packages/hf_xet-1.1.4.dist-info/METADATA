Metadata-Version: 2.4
Name: hf-xet
Version: 1.1.4
Classifier: Programming Language :: Rust
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Requires-Dist: pytest ; extra == 'tests'
Provides-Extra: tests
License-File: LICENSE
Summary: Fast transfer of large files with the Hugging Face Hub.
License: Apache-2.0
Requires-Python: >=3.8
Description-Content-Type: text/markdown; charset=UTF-8; variant=GFM
Project-URL: Homepage, https://github.com/huggingface/xet-core
Project-URL: Documentation, https://huggingface.co/docs/hub/en/storage-backends#using-xet-storage
Project-URL: Issues, https://github.com/huggingface/xet-core/issues
Project-URL: Repository, https://github.com/huggingface/xet-core.git

# Development Notes

* `pip install maturin`
* from this directory: `maturin develop`

