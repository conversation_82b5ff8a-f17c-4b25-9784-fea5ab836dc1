# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1alpha3NetworkDeviceData(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'hardware_address': 'str',
        'interface_name': 'str',
        'ips': 'list[str]'
    }

    attribute_map = {
        'hardware_address': 'hardwareAddress',
        'interface_name': 'interfaceName',
        'ips': 'ips'
    }

    def __init__(self, hardware_address=None, interface_name=None, ips=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha3NetworkDeviceData - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._hardware_address = None
        self._interface_name = None
        self._ips = None
        self.discriminator = None

        if hardware_address is not None:
            self.hardware_address = hardware_address
        if interface_name is not None:
            self.interface_name = interface_name
        if ips is not None:
            self.ips = ips

    @property
    def hardware_address(self):
        """Gets the hardware_address of this V1alpha3NetworkDeviceData.  # noqa: E501

        HardwareAddress represents the hardware address (e.g. MAC Address) of the device's network interface.  Must not be longer than 128 characters.  # noqa: E501

        :return: The hardware_address of this V1alpha3NetworkDeviceData.  # noqa: E501
        :rtype: str
        """
        return self._hardware_address

    @hardware_address.setter
    def hardware_address(self, hardware_address):
        """Sets the hardware_address of this V1alpha3NetworkDeviceData.

        HardwareAddress represents the hardware address (e.g. MAC Address) of the device's network interface.  Must not be longer than 128 characters.  # noqa: E501

        :param hardware_address: The hardware_address of this V1alpha3NetworkDeviceData.  # noqa: E501
        :type: str
        """

        self._hardware_address = hardware_address

    @property
    def interface_name(self):
        """Gets the interface_name of this V1alpha3NetworkDeviceData.  # noqa: E501

        InterfaceName specifies the name of the network interface associated with the allocated device. This might be the name of a physical or virtual network interface being configured in the pod.  Must not be longer than 256 characters.  # noqa: E501

        :return: The interface_name of this V1alpha3NetworkDeviceData.  # noqa: E501
        :rtype: str
        """
        return self._interface_name

    @interface_name.setter
    def interface_name(self, interface_name):
        """Sets the interface_name of this V1alpha3NetworkDeviceData.

        InterfaceName specifies the name of the network interface associated with the allocated device. This might be the name of a physical or virtual network interface being configured in the pod.  Must not be longer than 256 characters.  # noqa: E501

        :param interface_name: The interface_name of this V1alpha3NetworkDeviceData.  # noqa: E501
        :type: str
        """

        self._interface_name = interface_name

    @property
    def ips(self):
        """Gets the ips of this V1alpha3NetworkDeviceData.  # noqa: E501

        IPs lists the network addresses assigned to the device's network interface. This can include both IPv4 and IPv6 addresses. The IPs are in the CIDR notation, which includes both the address and the associated subnet mask. e.g.: \"*********/24\" for IPv4 and \"2001:db8::5/64\" for IPv6.  Must not contain more than 16 entries.  # noqa: E501

        :return: The ips of this V1alpha3NetworkDeviceData.  # noqa: E501
        :rtype: list[str]
        """
        return self._ips

    @ips.setter
    def ips(self, ips):
        """Sets the ips of this V1alpha3NetworkDeviceData.

        IPs lists the network addresses assigned to the device's network interface. This can include both IPv4 and IPv6 addresses. The IPs are in the CIDR notation, which includes both the address and the associated subnet mask. e.g.: \"*********/24\" for IPv4 and \"2001:db8::5/64\" for IPv6.  Must not contain more than 16 entries.  # noqa: E501

        :param ips: The ips of this V1alpha3NetworkDeviceData.  # noqa: E501
        :type: list[str]
        """

        self._ips = ips

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha3NetworkDeviceData):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha3NetworkDeviceData):
            return True

        return self.to_dict() != other.to_dict()
