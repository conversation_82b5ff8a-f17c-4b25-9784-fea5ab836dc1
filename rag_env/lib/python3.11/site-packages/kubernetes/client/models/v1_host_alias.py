# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1HostAlias(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'hostnames': 'list[str]',
        'ip': 'str'
    }

    attribute_map = {
        'hostnames': 'hostnames',
        'ip': 'ip'
    }

    def __init__(self, hostnames=None, ip=None, local_vars_configuration=None):  # noqa: E501
        """V1HostAlias - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._hostnames = None
        self._ip = None
        self.discriminator = None

        if hostnames is not None:
            self.hostnames = hostnames
        self.ip = ip

    @property
    def hostnames(self):
        """Gets the hostnames of this V1HostAlias.  # noqa: E501

        Hostnames for the above IP address.  # noqa: E501

        :return: The hostnames of this V1HostAlias.  # noqa: E501
        :rtype: list[str]
        """
        return self._hostnames

    @hostnames.setter
    def hostnames(self, hostnames):
        """Sets the hostnames of this V1HostAlias.

        Hostnames for the above IP address.  # noqa: E501

        :param hostnames: The hostnames of this V1HostAlias.  # noqa: E501
        :type: list[str]
        """

        self._hostnames = hostnames

    @property
    def ip(self):
        """Gets the ip of this V1HostAlias.  # noqa: E501

        IP address of the host file entry.  # noqa: E501

        :return: The ip of this V1HostAlias.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this V1HostAlias.

        IP address of the host file entry.  # noqa: E501

        :param ip: The ip of this V1HostAlias.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and ip is None:  # noqa: E501
            raise ValueError("Invalid value for `ip`, must not be `None`")  # noqa: E501

        self._ip = ip

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1HostAlias):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1HostAlias):
            return True

        return self.to_dict() != other.to_dict()
