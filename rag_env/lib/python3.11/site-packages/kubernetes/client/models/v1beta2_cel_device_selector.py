# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1beta2CELDeviceSelector(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'expression': 'str'
    }

    attribute_map = {
        'expression': 'expression'
    }

    def __init__(self, expression=None, local_vars_configuration=None):  # noqa: E501
        """V1beta2CELDeviceSelector - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._expression = None
        self.discriminator = None

        self.expression = expression

    @property
    def expression(self):
        """Gets the expression of this V1beta2CELDeviceSelector.  # noqa: E501

        Expression is a CEL expression which evaluates a single device. It must evaluate to true when the device under consideration satisfies the desired criteria, and false when it does not. Any other result is an error and causes allocation of devices to abort.  The expression's input is an object named \"device\", which carries the following properties:  - driver (string): the name of the driver which defines this device.  - attributes (map[string]object): the device's attributes, grouped by prefix    (e.g. device.attributes[\"dra.example.com\"] evaluates to an object with all    of the attributes which were prefixed by \"dra.example.com\".  - capacity (map[string]object): the device's capacities, grouped by prefix.  Example: Consider a device with driver=\"dra.example.com\", which exposes two attributes named \"model\" and \"ext.example.com/family\" and which exposes one capacity named \"modules\". This input to this expression would have the following fields:      device.driver     device.attributes[\"dra.example.com\"].model     device.attributes[\"ext.example.com\"].family     device.capacity[\"dra.example.com\"].modules  The device.driver field can be used to check for a specific driver, either as a high-level precondition (i.e. you only want to consider devices from this driver) or as part of a multi-clause expression that is meant to consider devices from different drivers.  The value type of each attribute is defined by the device definition, and users who write these expressions must consult the documentation for their specific drivers. The value type of each capacity is Quantity.  If an unknown prefix is used as a lookup in either device.attributes or device.capacity, an empty map will be returned. Any reference to an unknown field will cause an evaluation error and allocation to abort.  A robust expression should check for the existence of attributes before referencing them.  For ease of use, the cel.bind() function is enabled, and can be used to simplify expressions that access multiple attributes with the same domain. For example:      cel.bind(dra, device.attributes[\"dra.example.com\"], dra.someBool && dra.anotherBool)  The length of the expression must be smaller or equal to 10 Ki. The cost of evaluating it is also limited based on the estimated number of logical steps.  # noqa: E501

        :return: The expression of this V1beta2CELDeviceSelector.  # noqa: E501
        :rtype: str
        """
        return self._expression

    @expression.setter
    def expression(self, expression):
        """Sets the expression of this V1beta2CELDeviceSelector.

        Expression is a CEL expression which evaluates a single device. It must evaluate to true when the device under consideration satisfies the desired criteria, and false when it does not. Any other result is an error and causes allocation of devices to abort.  The expression's input is an object named \"device\", which carries the following properties:  - driver (string): the name of the driver which defines this device.  - attributes (map[string]object): the device's attributes, grouped by prefix    (e.g. device.attributes[\"dra.example.com\"] evaluates to an object with all    of the attributes which were prefixed by \"dra.example.com\".  - capacity (map[string]object): the device's capacities, grouped by prefix.  Example: Consider a device with driver=\"dra.example.com\", which exposes two attributes named \"model\" and \"ext.example.com/family\" and which exposes one capacity named \"modules\". This input to this expression would have the following fields:      device.driver     device.attributes[\"dra.example.com\"].model     device.attributes[\"ext.example.com\"].family     device.capacity[\"dra.example.com\"].modules  The device.driver field can be used to check for a specific driver, either as a high-level precondition (i.e. you only want to consider devices from this driver) or as part of a multi-clause expression that is meant to consider devices from different drivers.  The value type of each attribute is defined by the device definition, and users who write these expressions must consult the documentation for their specific drivers. The value type of each capacity is Quantity.  If an unknown prefix is used as a lookup in either device.attributes or device.capacity, an empty map will be returned. Any reference to an unknown field will cause an evaluation error and allocation to abort.  A robust expression should check for the existence of attributes before referencing them.  For ease of use, the cel.bind() function is enabled, and can be used to simplify expressions that access multiple attributes with the same domain. For example:      cel.bind(dra, device.attributes[\"dra.example.com\"], dra.someBool && dra.anotherBool)  The length of the expression must be smaller or equal to 10 Ki. The cost of evaluating it is also limited based on the estimated number of logical steps.  # noqa: E501

        :param expression: The expression of this V1beta2CELDeviceSelector.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and expression is None:  # noqa: E501
            raise ValueError("Invalid value for `expression`, must not be `None`")  # noqa: E501

        self._expression = expression

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1beta2CELDeviceSelector):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1beta2CELDeviceSelector):
            return True

        return self.to_dict() != other.to_dict()
