# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1VolumeMountStatus(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'mount_path': 'str',
        'name': 'str',
        'read_only': 'bool',
        'recursive_read_only': 'str'
    }

    attribute_map = {
        'mount_path': 'mountPath',
        'name': 'name',
        'read_only': 'readOnly',
        'recursive_read_only': 'recursiveReadOnly'
    }

    def __init__(self, mount_path=None, name=None, read_only=None, recursive_read_only=None, local_vars_configuration=None):  # noqa: E501
        """V1VolumeMountStatus - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._mount_path = None
        self._name = None
        self._read_only = None
        self._recursive_read_only = None
        self.discriminator = None

        self.mount_path = mount_path
        self.name = name
        if read_only is not None:
            self.read_only = read_only
        if recursive_read_only is not None:
            self.recursive_read_only = recursive_read_only

    @property
    def mount_path(self):
        """Gets the mount_path of this V1VolumeMountStatus.  # noqa: E501

        MountPath corresponds to the original VolumeMount.  # noqa: E501

        :return: The mount_path of this V1VolumeMountStatus.  # noqa: E501
        :rtype: str
        """
        return self._mount_path

    @mount_path.setter
    def mount_path(self, mount_path):
        """Sets the mount_path of this V1VolumeMountStatus.

        MountPath corresponds to the original VolumeMount.  # noqa: E501

        :param mount_path: The mount_path of this V1VolumeMountStatus.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and mount_path is None:  # noqa: E501
            raise ValueError("Invalid value for `mount_path`, must not be `None`")  # noqa: E501

        self._mount_path = mount_path

    @property
    def name(self):
        """Gets the name of this V1VolumeMountStatus.  # noqa: E501

        Name corresponds to the name of the original VolumeMount.  # noqa: E501

        :return: The name of this V1VolumeMountStatus.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this V1VolumeMountStatus.

        Name corresponds to the name of the original VolumeMount.  # noqa: E501

        :param name: The name of this V1VolumeMountStatus.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and name is None:  # noqa: E501
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def read_only(self):
        """Gets the read_only of this V1VolumeMountStatus.  # noqa: E501

        ReadOnly corresponds to the original VolumeMount.  # noqa: E501

        :return: The read_only of this V1VolumeMountStatus.  # noqa: E501
        :rtype: bool
        """
        return self._read_only

    @read_only.setter
    def read_only(self, read_only):
        """Sets the read_only of this V1VolumeMountStatus.

        ReadOnly corresponds to the original VolumeMount.  # noqa: E501

        :param read_only: The read_only of this V1VolumeMountStatus.  # noqa: E501
        :type: bool
        """

        self._read_only = read_only

    @property
    def recursive_read_only(self):
        """Gets the recursive_read_only of this V1VolumeMountStatus.  # noqa: E501

        RecursiveReadOnly must be set to Disabled, Enabled, or unspecified (for non-readonly mounts). An IfPossible value in the original VolumeMount must be translated to Disabled or Enabled, depending on the mount result.  # noqa: E501

        :return: The recursive_read_only of this V1VolumeMountStatus.  # noqa: E501
        :rtype: str
        """
        return self._recursive_read_only

    @recursive_read_only.setter
    def recursive_read_only(self, recursive_read_only):
        """Sets the recursive_read_only of this V1VolumeMountStatus.

        RecursiveReadOnly must be set to Disabled, Enabled, or unspecified (for non-readonly mounts). An IfPossible value in the original VolumeMount must be translated to Disabled or Enabled, depending on the mount result.  # noqa: E501

        :param recursive_read_only: The recursive_read_only of this V1VolumeMountStatus.  # noqa: E501
        :type: str
        """

        self._recursive_read_only = recursive_read_only

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1VolumeMountStatus):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1VolumeMountStatus):
            return True

        return self.to_dict() != other.to_dict()
