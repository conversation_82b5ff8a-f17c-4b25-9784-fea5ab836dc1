# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1alpha3DeviceTaintSelector(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'device': 'str',
        'device_class_name': 'str',
        'driver': 'str',
        'pool': 'str',
        'selectors': 'list[V1alpha3DeviceSelector]'
    }

    attribute_map = {
        'device': 'device',
        'device_class_name': 'deviceClassName',
        'driver': 'driver',
        'pool': 'pool',
        'selectors': 'selectors'
    }

    def __init__(self, device=None, device_class_name=None, driver=None, pool=None, selectors=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha3DeviceTaintSelector - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._device = None
        self._device_class_name = None
        self._driver = None
        self._pool = None
        self._selectors = None
        self.discriminator = None

        if device is not None:
            self.device = device
        if device_class_name is not None:
            self.device_class_name = device_class_name
        if driver is not None:
            self.driver = driver
        if pool is not None:
            self.pool = pool
        if selectors is not None:
            self.selectors = selectors

    @property
    def device(self):
        """Gets the device of this V1alpha3DeviceTaintSelector.  # noqa: E501

        If device is set, only devices with that name are selected. This field corresponds to slice.spec.devices[].name.  Setting also driver and pool may be required to avoid ambiguity, but is not required.  # noqa: E501

        :return: The device of this V1alpha3DeviceTaintSelector.  # noqa: E501
        :rtype: str
        """
        return self._device

    @device.setter
    def device(self, device):
        """Sets the device of this V1alpha3DeviceTaintSelector.

        If device is set, only devices with that name are selected. This field corresponds to slice.spec.devices[].name.  Setting also driver and pool may be required to avoid ambiguity, but is not required.  # noqa: E501

        :param device: The device of this V1alpha3DeviceTaintSelector.  # noqa: E501
        :type: str
        """

        self._device = device

    @property
    def device_class_name(self):
        """Gets the device_class_name of this V1alpha3DeviceTaintSelector.  # noqa: E501

        If DeviceClassName is set, the selectors defined there must be satisfied by a device to be selected. This field corresponds to class.metadata.name.  # noqa: E501

        :return: The device_class_name of this V1alpha3DeviceTaintSelector.  # noqa: E501
        :rtype: str
        """
        return self._device_class_name

    @device_class_name.setter
    def device_class_name(self, device_class_name):
        """Sets the device_class_name of this V1alpha3DeviceTaintSelector.

        If DeviceClassName is set, the selectors defined there must be satisfied by a device to be selected. This field corresponds to class.metadata.name.  # noqa: E501

        :param device_class_name: The device_class_name of this V1alpha3DeviceTaintSelector.  # noqa: E501
        :type: str
        """

        self._device_class_name = device_class_name

    @property
    def driver(self):
        """Gets the driver of this V1alpha3DeviceTaintSelector.  # noqa: E501

        If driver is set, only devices from that driver are selected. This fields corresponds to slice.spec.driver.  # noqa: E501

        :return: The driver of this V1alpha3DeviceTaintSelector.  # noqa: E501
        :rtype: str
        """
        return self._driver

    @driver.setter
    def driver(self, driver):
        """Sets the driver of this V1alpha3DeviceTaintSelector.

        If driver is set, only devices from that driver are selected. This fields corresponds to slice.spec.driver.  # noqa: E501

        :param driver: The driver of this V1alpha3DeviceTaintSelector.  # noqa: E501
        :type: str
        """

        self._driver = driver

    @property
    def pool(self):
        """Gets the pool of this V1alpha3DeviceTaintSelector.  # noqa: E501

        If pool is set, only devices in that pool are selected.  Also setting the driver name may be useful to avoid ambiguity when different drivers use the same pool name, but this is not required because selecting pools from different drivers may also be useful, for example when drivers with node-local devices use the node name as their pool name.  # noqa: E501

        :return: The pool of this V1alpha3DeviceTaintSelector.  # noqa: E501
        :rtype: str
        """
        return self._pool

    @pool.setter
    def pool(self, pool):
        """Sets the pool of this V1alpha3DeviceTaintSelector.

        If pool is set, only devices in that pool are selected.  Also setting the driver name may be useful to avoid ambiguity when different drivers use the same pool name, but this is not required because selecting pools from different drivers may also be useful, for example when drivers with node-local devices use the node name as their pool name.  # noqa: E501

        :param pool: The pool of this V1alpha3DeviceTaintSelector.  # noqa: E501
        :type: str
        """

        self._pool = pool

    @property
    def selectors(self):
        """Gets the selectors of this V1alpha3DeviceTaintSelector.  # noqa: E501

        Selectors contains the same selection criteria as a ResourceClaim. Currently, CEL expressions are supported. All of these selectors must be satisfied.  # noqa: E501

        :return: The selectors of this V1alpha3DeviceTaintSelector.  # noqa: E501
        :rtype: list[V1alpha3DeviceSelector]
        """
        return self._selectors

    @selectors.setter
    def selectors(self, selectors):
        """Sets the selectors of this V1alpha3DeviceTaintSelector.

        Selectors contains the same selection criteria as a ResourceClaim. Currently, CEL expressions are supported. All of these selectors must be satisfied.  # noqa: E501

        :param selectors: The selectors of this V1alpha3DeviceTaintSelector.  # noqa: E501
        :type: list[V1alpha3DeviceSelector]
        """

        self._selectors = selectors

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha3DeviceTaintSelector):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha3DeviceTaintSelector):
            return True

        return self.to_dict() != other.to_dict()
