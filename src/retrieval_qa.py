import requests
import config
from src.embeddings import EmbeddingManager

class RAGChatbot:
    def __init__(self, vector_store, max_tokens=None):
        self.vector_store = vector_store
        self.embedding_manager = EmbeddingManager()
        self.memory = []
        self.max_tokens = max_tokens or config.DEFAULT_MAX_TOKENS

    def chat(self, prompt):
        # Use text-based similarity search (ChromaDB will handle embeddings internally)
        docs = self.vector_store.similarity_search(prompt, k=config.SIMILARITY_SEARCH_K)

        # Filter and enhance context
        context = self._prepare_context(docs, prompt)
        answer = self.llm_answer(prompt, context)
        return {"answer": answer, "sources": docs}

    def _prepare_context(self, docs, prompt):
        """Prepare and optimize context for better responses"""
        if not docs:
            return "No relevant documents found."

        # Create structured context with source attribution
        context_parts = []
        for i, doc in enumerate(docs, 1):
            content = doc["page_content"].strip()
            source = doc.get("metadata", {}).get("source", f"Document {i}")

            # Add source attribution
            context_parts.append(f"[Source {i}: {source}]\n{content}")

        # Join with clear separators
        context = "\n\n" + "="*50 + "\n\n".join(context_parts)

        # Add context length info for the AI
        total_chars = len(context)
        context_header = f"[Context contains {len(docs)} relevant sections, {total_chars} characters total]\n\n"

        return context_header + context

    def llm_answer(self, prompt, context):
        url = f"{config.OLLAMA_BASE_URL}/api/generate"

        # Enhanced system prompt for better responses
        system_prompt = """You are an intelligent document assistant. Your task is to provide accurate, helpful, and well-structured answers based on the provided context.

INSTRUCTIONS:
1. Answer directly and concisely based ONLY on the provided context
2. If the context doesn't contain enough information, clearly state what's missing
3. Structure your response with clear paragraphs and bullet points when appropriate
4. Cite specific parts of the context when relevant
5. If asked about something not in the context, say "I don't have information about that in the provided documents"
6. Be conversational but professional
7. Provide actionable information when possible

CONTEXT:
{context}

QUESTION: {question}

ANSWER:"""

        user_prompt = system_prompt.format(context=context, question=prompt)
        payload = {
            "model": config.OLLAMA_MODEL,
            "prompt": user_prompt,
            "stream": False,  # Disable streaming to get a single JSON response
            "options": {
                "num_predict": self.max_tokens,  # Max tokens to generate
                "temperature": 0.7
            }
        }

        try:
            r = requests.post(url, json=payload, timeout=90)
            r.raise_for_status()  # Raise an exception for bad status codes
            data = r.json()
            return data.get("response", "[No answer returned]")
        except requests.exceptions.RequestException as e:
            return f"Error communicating with Ollama: {str(e)}"
        except Exception as e:
            return f"Error processing response: {str(e)}"

    def summarize_documents(self, max_docs=3):
        all_chunks = self.vector_store.collection.get(limit=max_docs)
        texts = [doc for doc in all_chunks['documents']]
        context = "\n\n".join(texts)
        return self.llm_summarize(context)

    def llm_summarize(self, context):
        url = f"{config.OLLAMA_BASE_URL}/api/generate"

        # Enhanced summarization prompt
        system_prompt = """You are an expert document summarizer. Create a comprehensive yet concise summary of the provided content.

INSTRUCTIONS:
1. Create a well-structured summary with clear sections
2. Include the main topics, key points, and important details
3. Use bullet points or numbered lists for clarity
4. Highlight any actionable items or conclusions
5. Maintain the original meaning and context
6. Keep the summary informative but readable
7. If there are multiple documents, organize by themes or topics

CONTENT TO SUMMARIZE:
{content}

COMPREHENSIVE SUMMARY:"""

        user_prompt = system_prompt.format(content=context)
        payload = {
            "model": config.OLLAMA_MODEL,
            "prompt": user_prompt,
            "stream": False,  # Disable streaming to get a single JSON response
            "options": {
                "num_predict": self.max_tokens,  # Max tokens to generate
                "temperature": 0.5  # Lower temperature for summaries
            }
        }

        try:
            r = requests.post(url, json=payload, timeout=90)
            r.raise_for_status()  # Raise an exception for bad status codes
            data = r.json()
            return data.get("response", "[No summary returned]")
        except requests.exceptions.RequestException as e:
            return f"Error communicating with Ollama: {str(e)}"
        except Exception as e:
            return f"Error processing response: {str(e)}"

    def clear_memory(self):
        self.memory = []

def create_rag_chatbot(vector_store):
    return RAGChatbot(vector_store)
