# Contributing to Smart Document Assistant

We love your input! We want to make contributing to this project as easy and transparent as possible, whether it's:

- Reporting a bug
- Discussing the current state of the code
- Submitting a fix
- Proposing new features
- Becoming a maintainer

## Development Process

We use GitHub to host code, to track issues and feature requests, as well as accept pull requests.

## Pull Requests

1. Fork the repo and create your branch from `main`.
2. If you've added code that should be tested, add tests.
3. If you've changed APIs, update the documentation.
4. Ensure the test suite passes.
5. Make sure your code lints.
6. Issue that pull request!

## Development Setup

1. **Clone your fork**:
   ```bash
   git clone https://github.com/yourusername/smart-document-assistant.git
   cd smart-document-assistant
   ```

2. **Set up environment**:
   ```bash
   python -m venv rag_env
   source rag_env/bin/activate  # On Windows: rag_env\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Install Ollama and models**:
   ```bash
   ollama pull llama3.1:latest
   ollama pull nomic-embed-text
   ```

4. **Run tests**:
   ```bash
   python test_setup.py
   python test_functionality.py
   ```

## Code Style

- Use Python 3.8+ features
- Follow PEP 8 style guidelines
- Add docstrings to all functions and classes
- Keep functions focused and small
- Use meaningful variable names

## Testing

- Add tests for new features
- Ensure all existing tests pass
- Test with different document types
- Verify performance optimizations work

## Reporting Bugs

We use GitHub issues to track public bugs. Report a bug by opening a new issue.

**Great Bug Reports** tend to have:

- A quick summary and/or background
- Steps to reproduce
- What you expected would happen
- What actually happens
- Notes (possibly including why you think this might be happening)

## Feature Requests

We welcome feature requests! Please:

1. Check if the feature already exists
2. Explain the use case
3. Describe the proposed solution
4. Consider implementation complexity

## License

By contributing, you agree that your contributions will be licensed under the MIT License.

## Questions?

Feel free to open an issue for any questions about contributing!
