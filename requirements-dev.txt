# Development dependencies
# Install with: pip install -r requirements-dev.txt

# Testing
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0

# Code quality
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0
mypy>=1.0.0

# Documentation
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0

# Development tools
pre-commit>=3.0.0
jupyter>=1.0.0
ipython>=8.0.0

# Performance testing
memory-profiler>=0.60.0
line-profiler>=4.0.0

# Security
bandit>=1.7.0
safety>=2.3.0
